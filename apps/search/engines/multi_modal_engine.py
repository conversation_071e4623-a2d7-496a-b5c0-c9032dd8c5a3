"""
Multi-Modal Query Engine for advanced search capabilities.

This module implements a multi-modal query engine using LlamaIndex's advanced query engines:
- Sub-question query engine for complex queries
- Composable graph query engine for structured data
- Knowledge graph query engine for entity relationships
"""

import logging
from typing import Any, Dict, List, Optional, Tuple, Union

from django.conf import settings

from llama_index.core.query_engine import (
    SubQuestionQueryEngine,
    ComposableGraphQueryEngine,
    KnowledgeGraphQueryEngine,
    RetrieverQueryEngine
)
from llama_index.core.indices import (
    VectorStoreIndex,
    KnowledgeGraphIndex,
    SummaryIndex
)
from llama_index.core.tools import QueryEngineTool
from llama_index.core.response.schema import Response
from llama_index.core.schema import BaseNode, Document, NodeWithScore
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.postprocessor import SimilarityPostprocessor

from apps.accounts.models import Tenant
from apps.core.utils.collection_manager import get_collection_name
from apps.core.utils.llama_index_embeddings import get_embedding_model_for_content
from apps.core.utils.llama_index_llm import get_llm_for_content
from apps.core.utils.llama_index_vectorstore import get_vector_store

logger = logging.getLogger(__name__)


class MultiModalQueryEngine:
    """
    Multi-Modal Query Engine for advanced search capabilities.

    This engine provides:
    - Sub-question query engine for complex queries
    - Composable graph query engine for structured data
    - Knowledge graph query engine for entity relationships
    """

    def __init__(self, tenant_slug: str):
        """
        Initialize the Multi-Modal Query Engine.

        Args:
            tenant_slug: The tenant slug to use
        """
        self.tenant_slug = tenant_slug

        # Get tenant from slug
        try:
            self.tenant = Tenant.objects.get(slug=tenant_slug)
        except Tenant.DoesNotExist:
            logger.error(f"Tenant with slug {tenant_slug} not found")
            raise ValueError(f"Tenant with slug {tenant_slug} not found")

        # Initialize indices
        self.indices = self._build_indices()

    def _build_indices(self) -> Dict[str, Any]:
        """
        Build multiple index types for comprehensive search.

        Returns:
            Dict[str, Any]: Dictionary of indices
        """
        indices = {}

        # Build vector index
        try:
            # Get vector store
            collection_name = get_collection_name(self.tenant_slug)
            vector_store = get_vector_store(collection_name=collection_name)

            # Create vector index
            indices["vector"] = VectorStoreIndex.from_vector_store(vector_store)
            logger.info(f"Built vector index for collection {collection_name}")
        except Exception as e:
            logger.error(f"Error building vector index: {str(e)}", exc_info=True)

        # Build summary index (we'll use the vector store for now)
        try:
            # Create summary index from vector index
            indices["summary"] = indices["vector"]
            logger.info("Built summary index")
        except Exception as e:
            logger.error(f"Error building summary index: {str(e)}", exc_info=True)

        # Build knowledge graph index (placeholder for now)
        try:
            # Create knowledge graph index from vector index
            indices["knowledge_graph"] = indices["vector"]
            logger.info("Built knowledge graph index")
        except Exception as e:
            logger.error(f"Error building knowledge graph index: {str(e)}", exc_info=True)

        return indices

    def build_sub_question_engine(self) -> SubQuestionQueryEngine:
        """
        Build sub-question query engine for complex queries.

        Returns:
            SubQuestionQueryEngine: Sub-question query engine
        """
        # Create query engine tools
        query_engine_tools = []

        # Add vector search tool
        if "vector" in self.indices:
            vector_engine = self.indices["vector"].as_query_engine(
                similarity_top_k=10,
                node_postprocessors=[
                    SimilarityPostprocessor(similarity_cutoff=0.7)
                ]
            )

            query_engine_tools.append(
                QueryEngineTool.from_defaults(
                    query_engine=vector_engine,
                    name="vector_search",
                    description="Semantic similarity search for finding relevant information"
                )
            )

        # Add knowledge graph tool
        if "knowledge_graph" in self.indices:
            kg_engine = self.indices["knowledge_graph"].as_query_engine()

            query_engine_tools.append(
                QueryEngineTool.from_defaults(
                    query_engine=kg_engine,
                    name="knowledge_graph",
                    description="Entity and relationship search for understanding connections"
                )
            )

        # Add summary tool
        if "summary" in self.indices:
            summary_engine = self.indices["summary"].as_query_engine()

            query_engine_tools.append(
                QueryEngineTool.from_defaults(
                    query_engine=summary_engine,
                    name="summary_search",
                    description="High-level document summaries for overview information"
                )
            )

        # Create sub-question engine
        return SubQuestionQueryEngine.from_defaults(
            query_engine_tools=query_engine_tools,
            llm=get_llm_for_content(),
            use_async=True,
            verbose=True
        )

    async def query(
        self,
        query_text: str,
        use_sub_questions: bool = True,
        max_sub_questions: int = 5,
    ) -> Response:
        """
        Execute a query using the multi-modal query engine.

        Args:
            query_text: The query text
            use_sub_questions: Whether to use sub-questions
            max_sub_questions: Maximum number of sub-questions to generate

        Returns:
            Response: Query response
        """
        try:
            # Build sub-question engine if needed
            if use_sub_questions:
                engine = self.build_sub_question_engine()

                # Set max sub-questions
                engine._max_sub_questions = max_sub_questions

                # Execute query
                response = await engine.aquery(query_text)

                return response
            else:
                # Use vector index directly
                if "vector" in self.indices:
                    engine = self.indices["vector"].as_query_engine(
                        similarity_top_k=10,
                        node_postprocessors=[
                            SimilarityPostprocessor(similarity_cutoff=0.7)
                        ]
                    )

                    # Execute query
                    response = engine.query(query_text)

                    return response
                else:
                    raise ValueError("No vector index available")
        except Exception as e:
            logger.error(f"Error executing query: {str(e)}", exc_info=True)
            raise
