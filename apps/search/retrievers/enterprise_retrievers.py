"""
Enterprise Retrievers for advanced retrieval strategies.

This module implements advanced retrieval strategies using LlamaIndex's retrievers:
- Vector index retriever for semantic search
- Keyword table retriever for keyword search
- Transform retriever for query transformation
- Router retriever for intelligent routing
- Recursive retriever for hierarchical content
"""

import logging
from typing import Any, Dict, List, Optional, Tuple, Union

from django.conf import settings

from llama_index.core.retrievers import (
    VectorIndexRetriever,
    KeywordTableRetriever,
    TransformRetriever,
    RouterRetriever,
    RecursiveRetriever,
    BM25Retriever
)
from llama_index.core.tools import RetrieverTool
from llama_index.core.selectors import LLMSingleSelector
from llama_index.core.postprocessor import (
    SimilarityPostprocessor,
    KeywordNodePostprocessor,
    MetadataReplacementPostProcessor,
    LongContextReorder
)
from llama_index.core.schema import Document, NodeWithScore
from llama_index.core.indices import VectorStoreIndex, KeywordTableIndex

from apps.accounts.models import Tenant
from apps.core.utils.collection_manager import get_collection_name
from apps.core.utils.llama_index_embeddings import get_embedding_model_for_content
from apps.core.utils.llama_index_llm import get_llm_for_content
from apps.core.utils.llama_index_vectorstore import get_vector_store

logger = logging.getLogger(__name__)


class EnterpriseRetrieverEngine:
    """
    Enterprise Retriever Engine for advanced retrieval strategies.

    This engine provides:
    - Hybrid retrieval with multiple strategies
    - Post-processing pipeline for retrieved nodes
    - Intelligent routing for query-specific retrieval
    """

    def __init__(self, tenant_slug: str):
        """
        Initialize the Enterprise Retriever Engine.

        Args:
            tenant_slug: The tenant slug to use
        """
        self.tenant_slug = tenant_slug

        # Get tenant from slug
        try:
            self.tenant = Tenant.objects.get(slug=tenant_slug)
        except Tenant.DoesNotExist:
            logger.error(f"Tenant with slug {tenant_slug} not found")
            raise ValueError(f"Tenant with slug {tenant_slug} not found")

        # Initialize indices
        self.vector_index = self._get_vector_index()
        self.keyword_index = self._get_keyword_index()

    def _get_vector_index(self) -> Optional[VectorStoreIndex]:
        """
        Get vector index.

        Returns:
            Optional[VectorStoreIndex]: Vector index
        """
        try:
            # Get vector store
            collection_name = get_collection_name(self.tenant_slug)
            vector_store = get_vector_store(collection_name=collection_name)

            # Create vector index
            return VectorStoreIndex.from_vector_store(vector_store)
        except Exception as e:
            logger.error(f"Error getting vector index: {str(e)}", exc_info=True)
            return None

    def _get_keyword_index(self) -> Optional[KeywordTableIndex]:
        """
        Get keyword index.

        Returns:
            Optional[KeywordTableIndex]: Keyword index
        """
        try:
            # For now, we'll use the vector index as a placeholder
            # In a real implementation, we would create a proper keyword index
            return self.vector_index
        except Exception as e:
            logger.error(f"Error getting keyword index: {str(e)}", exc_info=True)
            return None

    def build_hybrid_retriever(
        self,
        similarity_top_k: int = 20,
        use_reciprocal_rank_fusion: bool = True,
    ) -> RouterRetriever:
        """
        Build advanced hybrid retrieval with multiple strategies.

        Args:
            similarity_top_k: Number of results to return from vector search
            use_reciprocal_rank_fusion: Whether to use reciprocal rank fusion

        Returns:
            RouterRetriever: Router retriever
        """
        retriever_tools = []

        # Add vector retriever
        if self.vector_index:
            vector_retriever = VectorIndexRetriever(
                index=self.vector_index,
                similarity_top_k=similarity_top_k,
                embed_model=get_embedding_model_for_content()
            )

            retriever_tools.append(
                RetrieverTool.from_defaults(
                    retriever=vector_retriever,
                    name="vector_retriever",
                    description="Semantic similarity search for finding relevant information"
                )
            )

        # Add keyword retriever
        if self.keyword_index:
            keyword_retriever = BM25Retriever.from_defaults(
                docstore=self.vector_index.docstore,
                similarity_top_k=similarity_top_k,
            )

            retriever_tools.append(
                RetrieverTool.from_defaults(
                    retriever=keyword_retriever,
                    name="keyword_retriever",
                    description="Keyword-based search for finding exact matches"
                )
            )

        # Create router retriever
        return RouterRetriever(
            selector=LLMSingleSelector.from_defaults(llm=get_llm_for_content()),
            retriever_tools=retriever_tools,
            verbose=True
        )

    def build_postprocessors(
        self,
        similarity_cutoff: float = 0.7,
        exclude_keywords: Optional[List[str]] = None,
    ) -> List[Any]:
        """
        Build post-processing pipeline for retrieved nodes.

        Args:
            similarity_cutoff: Similarity cutoff for filtering nodes
            exclude_keywords: Keywords to exclude from results

        Returns:
            List[Any]: List of post-processors
        """
        postprocessors = []

        # Add similarity post-processor
        postprocessors.append(
            SimilarityPostprocessor(similarity_cutoff=similarity_cutoff)
        )

        # Add keyword post-processor if exclude_keywords is provided
        if exclude_keywords:
            postprocessors.append(
                KeywordNodePostprocessor(
                    exclude_keywords=exclude_keywords
                )
            )

        # Add long context reorder post-processor
        postprocessors.append(LongContextReorder())

        return postprocessors

    def retrieve(
        self,
        query_text: str,
        top_k: int = 20,
        similarity_cutoff: float = 0.7,
        use_hybrid: bool = True,
        metadata_filter: Optional[Dict[str, Any]] = None,
    ) -> List[NodeWithScore]:
        """
        Retrieve nodes using the enterprise retriever engine.

        Args:
            query_text: The query text
            top_k: Number of results to return
            similarity_cutoff: Similarity cutoff for filtering nodes
            use_hybrid: Whether to use hybrid retrieval
            metadata_filter: Filter to apply to search results

        Returns:
            List[NodeWithScore]: List of retrieved nodes with scores
        """
        try:
            # Build hybrid retriever if requested
            if use_hybrid:
                retriever = self.build_hybrid_retriever(
                    similarity_top_k=top_k
                )
            else:
                # Use vector retriever directly
                retriever = VectorIndexRetriever(
                    index=self.vector_index,
                    similarity_top_k=top_k,
                    embed_model=get_embedding_model_for_content()
                )

            # Build post-processors
            postprocessors = self.build_postprocessors(
                similarity_cutoff=similarity_cutoff
            )

            # Apply metadata filter if provided
            if metadata_filter and hasattr(retriever, "filters"):
                retriever.filters = metadata_filter

            # Retrieve nodes
            nodes = retriever.retrieve(query_text)

            # Apply post-processors
            for postprocessor in postprocessors:
                nodes = postprocessor.postprocess_nodes(nodes, query_text)

            return nodes
        except Exception as e:
            logger.error(f"Error retrieving nodes: {str(e)}", exc_info=True)
            return []
