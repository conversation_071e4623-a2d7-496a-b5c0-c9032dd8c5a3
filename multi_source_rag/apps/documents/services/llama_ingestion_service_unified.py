"""
Unified LlamaIndex Ingestion Service - Production Ready

This service completely replaces custom logic with LlamaIndex end-to-end implementation:
- LlamaIndex IngestionPipeline for all document processing
- Advanced node parsers for intelligent chunking
- Automatic metadata extraction and enrichment
- Specialized pipelines for different content types
- Production-ready error handling and monitoring
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union

from django.db import transaction
from django.contrib.auth.models import User

# LlamaIndex imports
from llama_index.core import Document
from llama_index.core.ingestion import IngestionPipeline
from llama_index.core.node_parser import (
    SentenceSplitter,
    CodeSplitter,
    SemanticSplitterNodeParser
)
# Removed LLM-dependent extractors to work without OpenAI API key
# from llama_index.core.extractors import (
#     TitleExtractor,
#     KeywordExtractor,
#     SummaryExtractor,
#     QuestionsAnsweredExtractor
# )

# App imports
from apps.accounts.models import Tenant
from apps.core.utils.collection_manager import get_collection_name
from apps.core.utils.llama_index_embeddings import get_embedding_model_for_content
from apps.core.utils.llama_index_vectorstore import get_vector_store
from apps.documents.interfaces.factory import DocumentSourceFactory
from apps.documents.interfaces.local_factory import LocalDocumentSourceFactory
from apps.documents.models import (
    DocumentChunk,
    DocumentProcessingJob,
    DocumentSource,
    RawDocument
)

logger = logging.getLogger(__name__)


class UnifiedLlamaIndexIngestionService:
    """
    Production-ready LlamaIndex ingestion service that replaces all custom logic.

    Features:
    - Specialized ingestion pipelines for different content types
    - Advanced chunking with LlamaIndex node parsers
    - Automatic metadata extraction and enrichment
    - Comprehensive error handling and monitoring
    - Data integrity guarantees
    """

    def __init__(self, tenant: Union[Tenant, str], user: Optional[User] = None):
        """
        Initialize the unified ingestion service.

        Args:
            tenant: Tenant object or tenant slug
            user: User performing the ingestion
        """
        if isinstance(tenant, str):
            try:
                self.tenant = Tenant.objects.get(slug=tenant)
            except Tenant.DoesNotExist:
                raise ValueError(f"Tenant with slug '{tenant}' not found")
        else:
            self.tenant = tenant

        self.user = user
        self.tenant_slug = self.tenant.slug

        # Initialize specialized pipelines
        self.pipelines = self._build_content_pipelines()

        # Processing statistics
        self.stats = {
            "documents_processed": 0,
            "documents_failed": 0,
            "chunks_created": 0,
            "processing_start_time": None,
            "processing_end_time": None
        }

    def _build_content_pipelines(self) -> Dict[str, IngestionPipeline]:
        """
        Build specialized ingestion pipelines for different content types.

        Returns:
            Dictionary of content type to IngestionPipeline mappings
        """
        pipelines = {}

        # Conversation pipeline (Slack, chat, etc.)
        pipelines["conversation"] = self._build_conversation_pipeline()

        # Code pipeline (GitHub, code files)
        pipelines["code"] = self._build_code_pipeline()

        # Document pipeline (general documents, markdown, etc.)
        pipelines["document"] = self._build_document_pipeline()

        return pipelines

    def _build_conversation_pipeline(self) -> IngestionPipeline:
        """Build specialized pipeline for conversation data."""
        collection_name = get_collection_name(self.tenant_slug, intent="conversation")
        vector_store = get_vector_store(collection_name=collection_name)
        embed_model = get_embedding_model_for_content(content_type="conversation")

        return IngestionPipeline(
            transformations=[
                SentenceSplitter(
                    chunk_size=1024,
                    chunk_overlap=200,
                    separator=" "
                ),
                # Removed LLM-dependent extractors to work without OpenAI API key
                # TitleExtractor(nodes=5),
                # KeywordExtractor(keywords=10),
                # QuestionsAnsweredExtractor(questions=3),
                embed_model,
            ],
            vector_store=vector_store
        )

    def _build_code_pipeline(self) -> IngestionPipeline:
        """Build specialized pipeline for code content."""
        collection_name = get_collection_name(self.tenant_slug, intent="code")
        vector_store = get_vector_store(collection_name=collection_name)
        embed_model = get_embedding_model_for_content(content_type="code")

        # Use a generic code splitter that will be configured per document
        return IngestionPipeline(
            transformations=[
                SentenceSplitter(  # Will be replaced with CodeSplitter per document
                    chunk_size=2000,
                    chunk_overlap=200,
                    separator="\n"
                ),
                # Removed LLM-dependent extractors to work without OpenAI API key
                # TitleExtractor(),
                # KeywordExtractor(keywords=15),
                # SummaryExtractor(summaries=["prev", "self"]),
                embed_model,
            ],
            vector_store=vector_store
        )

    def _build_document_pipeline(self) -> IngestionPipeline:
        """Build specialized pipeline for general documents."""
        collection_name = get_collection_name(self.tenant_slug, intent="document")
        vector_store = get_vector_store(collection_name=collection_name)
        embed_model = get_embedding_model_for_content(content_type="document")

        return IngestionPipeline(
            transformations=[
                SemanticSplitterNodeParser(
                    embed_model=embed_model,
                    buffer_size=1,
                    breakpoint_percentile_threshold=95
                ),
                # Removed LLM-dependent extractors to work without OpenAI API key
                # TitleExtractor(),
                # KeywordExtractor(),
                # SummaryExtractor(),
                embed_model,
            ],
            vector_store=vector_store
        )

    def process_source(
        self,
        source: DocumentSource,
        job: Optional[DocumentProcessingJob] = None,
        batch_size: int = 100,
        **kwargs
    ) -> Tuple[int, int]:
        """
        Process a document source using LlamaIndex pipelines.

        Args:
            source: Document source to process
            job: Processing job to update
            batch_size: Number of documents to process in a batch
            **kwargs: Additional arguments for processing

        Returns:
            Tuple of (processed_count, failed_count)
        """
        self.stats["processing_start_time"] = datetime.now()

        # Update job status
        if job:
            job.status = "processing"
            job.started_at = self.stats["processing_start_time"]
            job.save()

        try:
            # Create source interface
            if source.source_type.startswith("local_"):
                source_interface = LocalDocumentSourceFactory.create_source(
                    source.source_type.replace("local_", ""), source.config
                )
            else:
                source_interface = DocumentSourceFactory.create_source(
                    source.source_type, source.config
                )

            # Fetch documents
            documents = source_interface.fetch_documents(**kwargs)

            # Process documents in batches
            processed = 0
            failed = 0
            total_documents = len(documents)

            for i in range(0, total_documents, batch_size):
                batch = documents[i:i + batch_size]
                batch_processed, batch_failed = self._process_document_batch(
                    source, batch, job
                )

                processed += batch_processed
                failed += batch_failed

                # Update job progress
                if job:
                    job.documents_processed = processed
                    job.documents_failed = failed
                    job.save()

                logger.info(f"Batch {i//batch_size + 1}: {batch_processed} processed, {batch_failed} failed")

            # Update final statistics
            self.stats["documents_processed"] = processed
            self.stats["documents_failed"] = failed
            self.stats["processing_end_time"] = datetime.now()

            # Update job status
            if job:
                job.status = "completed"
                job.completed_at = self.stats["processing_end_time"]
                job.documents_processed = processed
                job.documents_failed = failed
                job.save()

            # Update source last synced
            source.last_synced = self.stats["processing_end_time"]
            source.save()

            logger.info(f"Processing completed: {processed} processed, {failed} failed")
            return processed, failed

        except Exception as e:
            self.stats["processing_end_time"] = datetime.now()

            if job:
                job.status = "failed"
                job.error_message = str(e)
                job.completed_at = self.stats["processing_end_time"]
                job.save()

            logger.error(f"Processing failed: {str(e)}")
            raise

    def _process_document_batch(
        self,
        source: DocumentSource,
        documents: List[Dict[str, Any]],
        job: Optional[DocumentProcessingJob] = None
    ) -> Tuple[int, int]:
        """
        Process a batch of documents using LlamaIndex pipelines.

        Args:
            source: Document source
            documents: List of documents to process
            job: Processing job for error logging

        Returns:
            Tuple of (processed_count, failed_count)
        """
        processed = 0
        failed = 0

        for document in documents:
            try:
                with transaction.atomic():
                    self._process_single_document(source, document)
                processed += 1
            except Exception as e:
                failed += 1
                error_msg = f"Error processing document {document.get('id', 'unknown')}: {str(e)}"
                logger.error(error_msg)

                if job:
                    if job.error_message:
                        job.error_message += f"\n{error_msg}"
                    else:
                        job.error_message = error_msg
                    job.save(update_fields=["error_message"])

        return processed, failed

    def _process_single_document(
        self,
        source: DocumentSource,
        document: Dict[str, Any]
    ) -> RawDocument:
        """
        Process a single document using LlamaIndex pipeline.

        Args:
            source: Document source
            document: Document data

        Returns:
            Processed RawDocument
        """
        # Validate document
        if not document.get("id"):
            raise ValueError("Document must have an 'id' field")

        content = document.get("content", "")
        if not content or not content.strip():
            raise ValueError("Document must have non-empty content")

        # Determine content type and pipeline
        content_type = self._determine_content_type(document, source)

        # For code content, detect language and create dynamic pipeline
        if content_type == "code":
            language = self._detect_programming_language(content, document.get("metadata", {}))
            pipeline = self._create_dynamic_code_pipeline(language)
            logger.info(f"Using dynamic code pipeline for language: {language}")
        else:
            pipeline = self._get_pipeline_for_content_type(content_type)

        # Create or update raw document
        raw_doc = self._create_or_update_raw_document(source, document, content_type)

        # Convert to LlamaIndex document
        llama_doc = Document(
            text=content,
            metadata={
                "document_id": str(raw_doc.id),
                "source_id": str(source.id),
                "source_type": source.source_type,
                "content_type": content_type,
                "tenant_id": str(self.tenant.id),
                **document.get("metadata", {})
            }
        )

        # Process with LlamaIndex pipeline with error handling
        try:
            nodes = pipeline.run(documents=[llama_doc])

            if not nodes:
                logger.warning(f"Pipeline returned no nodes for document {raw_doc.external_id}")
                # Create a fallback chunk to ensure data integrity
                nodes = self._create_fallback_nodes(llama_doc)

        except Exception as e:
            logger.error(f"Pipeline processing failed for document {raw_doc.external_id}: {str(e)}")
            # Create fallback nodes to prevent data loss
            nodes = self._create_fallback_nodes(llama_doc)

        # Store nodes as document chunks
        try:
            self._store_nodes_as_chunks(raw_doc, nodes)
        except Exception as e:
            logger.error(f"Failed to store chunks for document {raw_doc.external_id}: {str(e)}")
            raise ValueError(f"Failed to store document chunks: {str(e)}")

        return raw_doc

    def _create_fallback_nodes(self, llama_doc: Document) -> List[Any]:
        """
        Create fallback nodes when pipeline processing fails.

        Args:
            llama_doc: LlamaIndex document

        Returns:
            List of fallback nodes
        """
        from llama_index.core.schema import TextNode

        # Simple text splitting as fallback
        text = llama_doc.text
        chunk_size = 1000
        chunks = []

        for i in range(0, len(text), chunk_size):
            chunk_text = text[i:i + chunk_size]
            node = TextNode(
                text=chunk_text,
                metadata={
                    **llama_doc.metadata,
                    "fallback_chunk": True,
                    "chunk_index": len(chunks),
                    "processing_method": "fallback_splitting"
                }
            )
            chunks.append(node)

        logger.info(f"Created {len(chunks)} fallback nodes")
        return chunks

    def _detect_programming_language(self, content: str, metadata: Dict[str, Any]) -> str:
        """
        Detect programming language from content and metadata.

        Args:
            content: Document content
            metadata: Document metadata

        Returns:
            Detected programming language
        """
        # Check file extension first
        filename = metadata.get("filename", "")
        if filename:
            ext = filename.split(".")[-1].lower()
            language_map = {
                "py": "python",
                "js": "javascript",
                "ts": "typescript",
                "java": "java",
                "cpp": "cpp",
                "c": "c",
                "cs": "csharp",
                "go": "go",
                "rs": "rust",
                "rb": "ruby",
                "php": "php",
                "swift": "swift",
                "kt": "kotlin",
                "scala": "scala",
                "sh": "bash",
                "sql": "sql",
                "html": "html",
                "css": "css",
                "xml": "xml",
                "json": "json",
                "yaml": "yaml",
                "yml": "yaml"
            }
            if ext in language_map:
                return language_map[ext]

        # Check content patterns
        if "def " in content and "import " in content:
            return "python"
        elif "function " in content and ("var " in content or "let " in content or "const " in content):
            return "javascript"
        elif "public class " in content and "static void main" in content:
            return "java"
        elif "#include" in content and ("int main" in content or "void main" in content):
            return "cpp"
        elif "package main" in content and "func main" in content:
            return "go"
        elif "fn main" in content and "use " in content:
            return "rust"

        # Default to python for unknown code
        return "python"

    def _create_dynamic_code_pipeline(self, language: str) -> IngestionPipeline:
        """
        Create a dynamic code pipeline based on detected language.

        Args:
            language: Programming language

        Returns:
            IngestionPipeline configured for the language
        """
        collection_name = get_collection_name(self.tenant_slug, intent="code")
        vector_store = get_vector_store(collection_name=collection_name)
        embed_model = get_embedding_model_for_content(content_type="code")

        return IngestionPipeline(
            transformations=[
                CodeSplitter(
                    language=language,
                    chunk_lines=40,
                    chunk_overlap=15
                ),
                # Removed LLM-dependent extractors to work without OpenAI API key
                # TitleExtractor(),
                # KeywordExtractor(keywords=15),
                # SummaryExtractor(summaries=["prev", "self"]),
                embed_model,
            ],
            vector_store=vector_store
        )

    def _determine_content_type(
        self,
        document: Dict[str, Any],
        source: DocumentSource
    ) -> str:
        """
        Determine the content type for pipeline selection.

        Args:
            document: Document data
            source: Document source

        Returns:
            Content type string
        """
        # Check explicit content type
        if "content_type" in document:
            content_type = document["content_type"]
            if "slack" in content_type or "conversation" in content_type:
                return "conversation"
            elif "code" in content_type or "python" in content_type:
                return "code"
            else:
                return "document"

        # Infer from source type
        if source.source_type in ["slack", "local_slack"]:
            return "conversation"
        elif source.source_type in ["github", "local_github"]:
            return "code"
        else:
            return "document"

    def _get_pipeline_for_content_type(self, content_type: str) -> IngestionPipeline:
        """
        Get the appropriate pipeline for content type.

        Args:
            content_type: Content type

        Returns:
            IngestionPipeline for the content type
        """
        return self.pipelines.get(content_type, self.pipelines["document"])

    def _create_or_update_raw_document(
        self,
        source: DocumentSource,
        document: Dict[str, Any],
        content_type: str
    ) -> RawDocument:
        """
        Create or update a raw document record.

        Args:
            source: Document source
            document: Document data
            content_type: Determined content type

        Returns:
            RawDocument instance
        """
        external_id = document["id"]
        content = document.get("content", "")
        title = document.get("title", "Untitled Document")
        metadata = document.get("metadata", {})

        # Generate content hash for idempotency
        import hashlib
        content_hash = hashlib.md5(
            f"{content}|{title}|{str(metadata)}".encode()
        ).hexdigest()

        try:
            # Try to get existing document
            raw_doc = RawDocument.objects.get(
                tenant=self.tenant,
                source=source,
                external_id=external_id
            )

            # Check if content changed
            if raw_doc.content_hash == content_hash:
                logger.debug(f"Document {external_id} unchanged, skipping")
                return raw_doc

            # Update existing document
            raw_doc.title = title
            raw_doc.permalink = document.get("url")
            raw_doc.content_hash = content_hash
            raw_doc.content_type = content_type
            raw_doc.metadata = metadata
            raw_doc.save()

            # Update or create document content
            from apps.documents.models import DocumentContent
            content_obj, created = DocumentContent.objects.get_or_create(
                document=raw_doc,
                defaults={
                    'content': content,
                    'content_format': 'slack' if content_type == 'conversation' else 'text'
                }
            )
            if not created:
                content_obj.content = content
                content_obj.save()

        except RawDocument.DoesNotExist:
            # Create new document
            raw_doc = RawDocument.objects.create(
                tenant=self.tenant,
                source=source,
                external_id=external_id,
                title=title,
                permalink=document.get("url"),
                content_hash=content_hash,
                content_type=content_type,
                metadata=metadata
            )

            # Create document content
            from apps.documents.models import DocumentContent
            DocumentContent.objects.create(
                document=raw_doc,
                content=content,
                content_format='slack' if content_type == 'conversation' else 'text'
            )

        return raw_doc

    def _store_nodes_as_chunks(
        self,
        raw_doc: RawDocument,
        nodes: List[Any]
    ) -> List[DocumentChunk]:
        """
        Store LlamaIndex nodes as DocumentChunk records.

        Args:
            raw_doc: Raw document
            nodes: LlamaIndex nodes

        Returns:
            List of created DocumentChunk instances
        """
        # Delete existing chunks for this document
        DocumentChunk.objects.filter(document=raw_doc).delete()

        chunks = []
        for i, node in enumerate(nodes):
            chunk = DocumentChunk.objects.create(
                tenant=self.tenant,
                document=raw_doc,
                text=node.text,
                chunk_index=i,
                metadata={
                    "node_id": node.node_id,
                    "total_chunks": len(nodes),
                    **node.metadata
                }
            )
            chunks.append(chunk)

        self.stats["chunks_created"] += len(chunks)
        logger.debug(f"Created {len(chunks)} chunks for document {raw_doc.external_id}")

        return chunks

    def get_processing_stats(self) -> Dict[str, Any]:
        """
        Get processing statistics.

        Returns:
            Dictionary of processing statistics
        """
        stats = self.stats.copy()

        if stats["processing_start_time"] and stats["processing_end_time"]:
            duration = stats["processing_end_time"] - stats["processing_start_time"]
            stats["processing_duration_seconds"] = duration.total_seconds()

        return stats