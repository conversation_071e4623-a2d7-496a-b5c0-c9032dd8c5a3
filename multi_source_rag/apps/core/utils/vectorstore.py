"""
Vector store utilities for the Multi-Source RAG system.
"""

import logging
import uuid
from typing import Any, Dict, List, Optional, Tuple, Union

from apps.core.utils.domain_embeddings import (domain_embeddings_manager,
                                               get_domain_embedding_model)
from django.conf import settings
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_community.vectorstores import Qdrant
from langchain_core.documents import Document
from langchain_core.embeddings import Embeddings
from qdrant_client import QdrantClient
from qdrant_client.http import models as qdrant_models

logger = logging.getLogger(__name__)


def get_embedding_model(
    model_name: Optional[str] = None,
    content_type: Optional[str] = None,
    domain: Optional[str] = None,
    tenant_slug: Optional[str] = None,
) -> Embeddings:
    """
    Initialize and return an embedding model based on content type, domain, or tenant.

    Args:
        model_name: Name of the embedding model to use (overrides other parameters)
        content_type: Type of content to embed
        domain: Domain of content to embed
        tenant_slug: Tenant slug for tenant-specific models

    Returns:
        Embeddings: Appropriate embedding model
    """
    # Use the domain-specific embedding model manager
    try:
        return get_domain_embedding_model(
            content_type=content_type,
            domain=domain,
            tenant_slug=tenant_slug,
            model_name=model_name,
        )
    except Exception as e:
        logger.error(f"Error getting domain-specific embedding model: {str(e)}")
        # Fall back to default embedding model
        model_name = model_name or settings.EMBEDDING_MODEL_NAME

        embeddings = HuggingFaceEmbeddings(
            model_name=model_name,
            model_kwargs={"device": "cpu"},
            encode_kwargs={"normalize_embeddings": True},
        )

        return embeddings


def get_qdrant_client() -> QdrantClient:
    """
    Initialize and return a Qdrant client.

    Returns:
        QdrantClient: Initialized Qdrant client
    """
    client = QdrantClient(host=settings.QDRANT_HOST, port=settings.QDRANT_PORT)

    return client


def get_vector_store(
    collection_name: Optional[str] = None,
    content_type: Optional[str] = None,
    domain: Optional[str] = None,
    tenant_slug: Optional[str] = None,
    model_name: Optional[str] = None,
) -> Qdrant:
    """
    Initialize and return a Qdrant vector store with domain-specific embeddings.

    Args:
        collection_name: Name of the collection to use
        content_type: Type of content to embed (used for embedding selection)
        domain: Domain of content to embed (used for embedding selection)
        tenant_slug: Tenant slug for tenant-specific models
        model_name: Specific model name to use (overrides other parameters)

    Returns:
        Qdrant: Initialized vector store
    """
    collection_name = collection_name or settings.QDRANT_COLLECTION_NAME
    client = get_qdrant_client()

    # Get domain-specific embedding model
    embeddings = get_embedding_model(
        model_name=model_name,
        content_type=content_type,
        domain=domain,
        tenant_slug=tenant_slug,
    )

    # Check if collection exists, if not create it
    collections = client.get_collections().collections
    collection_names = [collection.name for collection in collections]

    if collection_name not in collection_names:
        # Create the collection with the appropriate dimension
        try:
            # Get the embedding dimension
            dimension = domain_embeddings_manager.get_model_dimension(
                model_name or embeddings.model_name
            )

            client.create_collection(
                collection_name=collection_name,
                vectors_config={"size": dimension, "distance": "Cosine"},
            )
            logger.info(
                f"Created collection {collection_name} with dimension {dimension}"
            )
        except Exception as e:
            # If collection already exists, that's fine
            if "already exists" not in str(e).lower():
                logger.error(f"Error creating collection: {str(e)}")
                # Continue anyway as the collection might already exist

    # Initialize the vector store with domain-specific embeddings
    vector_store = Qdrant(
        client=client, collection_name=collection_name, embeddings=embeddings
    )

    logger.info(
        f"Initialized vector store for collection {collection_name} with {content_type or domain or 'default'} embedding model"
    )

    return vector_store


def add_documents_to_vector_store(
    documents: List[Document],
    collection_name: Optional[str] = None,
    content_type: Optional[str] = None,
    domain: Optional[str] = None,
    tenant_slug: Optional[str] = None,
    model_name: Optional[str] = None,
    ids: Optional[List[str]] = None,
) -> List[str]:
    """
    Add documents to the vector store using domain-specific embeddings.

    Args:
        documents: List of documents to add
        collection_name: Name of the collection to use
        content_type: Type of content to embed (used for embedding selection)
        domain: Domain of content to embed (used for embedding selection)
        tenant_slug: Tenant slug for tenant-specific models
        model_name: Specific model name to use (overrides other parameters)

    Returns:
        List[str]: List of document IDs
    """
    # Get Qdrant client
    client = get_qdrant_client()

    # Get domain-specific embedding model
    embeddings = get_embedding_model(
        model_name=model_name,
        content_type=content_type,
        domain=domain,
        tenant_slug=tenant_slug,
    )

    # Use default collection name if not provided
    collection_name = collection_name or settings.QDRANT_COLLECTION_NAME

    # Check if collection exists, if not create it
    collections = client.get_collections().collections
    collection_names = [collection.name for collection in collections]

    if collection_name not in collection_names:
        # Create the collection with the appropriate dimension
        try:
            # Get the embedding dimension
            dimension = domain_embeddings_manager.get_model_dimension(
                model_name or embeddings.model_name
            )

            client.create_collection(
                collection_name=collection_name,
                vectors_config={"size": dimension, "distance": "Cosine"},
            )
            logger.info(
                f"Created collection {collection_name} with dimension {dimension}"
            )
        except Exception as e:
            # If collection already exists, that's fine
            if "already exists" not in str(e).lower():
                logger.error(f"Error creating collection: {str(e)}")
                # Continue anyway as the collection might already exist

    # Initialize the vector store with the domain-specific embeddings
    vector_store = Qdrant(
        client=client, collection_name=collection_name, embeddings=embeddings
    )

    # Determine content type from documents if not provided
    if not content_type and documents:
        # Try to get content type from metadata
        for doc in documents:
            if "content_type" in doc.metadata:
                content_type = doc.metadata["content_type"]
                logger.info(
                    f"Using content type from document metadata: {content_type}"
                )
                break

    # Log embedding information
    logger.info(
        f"Adding {len(documents)} documents to collection {collection_name} with {content_type or domain or 'default'} embedding model"
    )

    # Add documents to the vector store
    # Use provided IDs if available, otherwise generate them
    if ids is None:
        ids = []

        for doc in documents:
            # Always generate a UUID for Qdrant to avoid integer ID issues
            uuid_id = str(uuid.uuid4())
            ids.append(uuid_id)

            # Store the original chunk_id in metadata if available
            if "chunk_id" in doc.metadata:
                doc.metadata["original_chunk_id"] = doc.metadata["chunk_id"]

            # Store the UUID in the vector_id field for later retrieval
            doc.metadata["vector_id"] = uuid_id

            logger.info(f"Using UUID {uuid_id} for vector store ID")
    else:
        # Use the provided IDs, but ensure they are strings
        string_ids = [str(id_val) for id_val in ids]
        ids = string_ids

        logger.info(f"Using {len(ids)} provided IDs for vector store")

        # Store the IDs in the metadata for later retrieval
        for i, doc in enumerate(documents):
            if i < len(ids):
                # Store the original chunk_id in metadata if available
                if "chunk_id" in doc.metadata:
                    doc.metadata["original_chunk_id"] = doc.metadata["chunk_id"]

                # Store the string ID in the vector_id field
                doc.metadata["vector_id"] = ids[i]

    # Add documents to the vector store with the IDs
    vector_store.add_documents(documents=documents, ids=ids)

    logger.info(f"Added {len(documents)} documents to vector store with IDs")

    return ids


def search_vector_store(
    query: str,
    collection_name: Optional[str] = None,
    metadata_filter: Optional[Dict[str, Any]] = None,
    k: int = 5,
    content_type: Optional[str] = None,
    domain: Optional[str] = None,
    tenant_slug: Optional[str] = None,
    model_name: Optional[str] = None,
    use_cache: bool = True,
) -> List[Tuple[Document, float]]:
    """
    Search the vector store for documents similar to the query using domain-specific embeddings.

    Args:
        query: Query string
        collection_name: Name of the collection to use
        metadata_filter: Metadata filter to apply
        k: Number of results to return
        content_type: Type of content to search for (used for embedding selection)
        domain: Domain of content to search for (used for embedding selection)
        tenant_slug: Tenant slug for tenant-specific models
        model_name: Specific model name to use (overrides other parameters)
        use_cache: Whether to use search result caching

    Returns:
        List[Tuple[Document, float]]: List of documents and their similarity scores
    """
    # Use default collection name if not provided
    if not collection_name:
        collection_name = settings.QDRANT_COLLECTION_NAME

    # Check cache first if enabled
    if use_cache:
        from apps.core.utils.search_cache import get_cached_search_results
        cached_results = get_cached_search_results(
            query=query,
            collection_name=collection_name,
            metadata_filter=metadata_filter,
            k=k,
            content_type=content_type,
            tenant_slug=tenant_slug
        )
        if cached_results is not None:
            return cached_results

    # Get Qdrant client
    client = get_qdrant_client()

    # Get domain-specific embedding model
    embeddings = get_embedding_model(
        model_name=model_name,
        content_type=content_type,
        domain=domain,
        tenant_slug=tenant_slug,
    )

    # Check if collection exists
    collections = client.get_collections().collections
    collection_names = [collection.name for collection in collections]

    if collection_name not in collection_names:
        logger.warning(
            f"Collection {collection_name} not found. Available collections: {collection_names}"
        )
        if not collection_names:
            return []
        collection_name = collection_names[0]
        logger.info(f"Using collection {collection_name} instead")

    # Embed the query with domain-specific model
    query_vector = embeddings.embed_query(query)

    # Convert metadata filter to Qdrant format
    qdrant_filter = None
    if metadata_filter:
        qdrant_filter = _validate_and_convert_metadata_filter(metadata_filter)
        logger.debug(f"Using metadata filter: {qdrant_filter}")

    # Search directly with the Qdrant client
    try:
        # Log available collections for debugging
        logger.debug(f"Available collections: {collection_names}")
        logger.info(
            f"Searching collection {collection_name} with {content_type or domain or 'default'} embedding model"
        )

        # Search
        results = client.search(
            collection_name=collection_name,
            query_vector=query_vector,
            limit=k,
            query_filter=qdrant_filter,
        )

        # Convert to LangChain format
        langchain_results = []
        for result in results:
            point_id = result.id
            score = result.score
            payload = result.payload

            # Create Document
            text = payload.get("text", "")
            metadata = {k: v for k, v in payload.items() if k != "text"}

            # Always include the vector_id for reference
            if "vector_id" not in metadata and point_id:
                metadata["vector_id"] = point_id

            # Ensure chunk_id is included in metadata
            if "chunk_id" not in metadata:
                # Try to get chunk_id from document_id or other fields
                if "document_chunk_id" in metadata:
                    metadata["chunk_id"] = metadata["document_chunk_id"]
                elif "vector_id" in metadata:
                    # Try to find the actual chunk_id from EmbeddingMetadata
                    try:
                        from apps.documents.models import EmbeddingMetadata

                        embedding = EmbeddingMetadata.objects.filter(
                            vector_id=metadata["vector_id"]
                        ).first()
                        if embedding and embedding.chunk:
                            metadata["chunk_id"] = str(embedding.chunk.id)
                            logger.debug(
                                f"Found chunk_id {metadata['chunk_id']} from EmbeddingMetadata for vector_id {metadata['vector_id']}"
                            )
                        else:
                            # Fallback to using vector_id as chunk_id
                            metadata["chunk_id"] = metadata["vector_id"]
                    except Exception as e:
                        logger.warning(
                            f"Error looking up chunk_id from vector_id: {str(e)}"
                        )
                        metadata["chunk_id"] = metadata["vector_id"]
                elif point_id:
                    # Try to find the actual chunk_id from EmbeddingMetadata
                    try:
                        from apps.documents.models import EmbeddingMetadata

                        embedding = EmbeddingMetadata.objects.filter(
                            vector_id=point_id
                        ).first()
                        if embedding and embedding.chunk:
                            metadata["chunk_id"] = str(embedding.chunk.id)
                            metadata["vector_id"] = point_id  # Also store the vector_id
                            logger.debug(
                                f"Found chunk_id {metadata['chunk_id']} from EmbeddingMetadata for point_id {point_id}"
                            )
                        else:
                            # Use point_id as a last resort
                            metadata["chunk_id"] = point_id
                            metadata["vector_id"] = point_id
                            logger.warning(f"Using point_id as chunk_id: {point_id}")
                    except Exception as e:
                        logger.warning(
                            f"Error looking up chunk_id from point_id: {str(e)}"
                        )
                        # Use point_id as a last resort
                        metadata["chunk_id"] = point_id
                        metadata["vector_id"] = point_id
                        logger.warning(f"Using point_id as chunk_id: {point_id}")
                else:
                    logger.warning(f"No chunk_id found for document with score {score}")

            # Log the mapping for debugging
            if "chunk_id" in metadata and "vector_id" in metadata:
                logger.debug(
                    f"Mapped vector_id {metadata['vector_id']} to chunk_id {metadata['chunk_id']}"
                )

            # Create the document
            doc = Document(page_content=text, metadata=metadata)

            # Ensure the document has the chunk_id in its metadata
            if "chunk_id" in metadata and "chunk_id" not in doc.metadata:
                doc.metadata["chunk_id"] = metadata["chunk_id"]

            # Add to results
            langchain_results.append((doc, score))

        logger.info(
            f"Found {len(langchain_results)} results in collection {collection_name}"
        )

        # Cache the results if caching is enabled
        if use_cache and langchain_results:
            from apps.core.utils.search_cache import cache_search_results
            cache_search_results(
                query=query,
                collection_name=collection_name,
                results=langchain_results,
                metadata_filter=metadata_filter,
                k=k,
                content_type=content_type,
                tenant_slug=tenant_slug
            )

        return langchain_results
    except Exception as e:
        logger.error(f"Error searching vector store: {str(e)}")
        return []


def _validate_and_convert_metadata_filter(
    metadata_filter: Dict[str, Any]
) -> Optional[Dict[str, Any]]:
    """
    Validate and convert metadata filter to Qdrant format.

    Args:
        metadata_filter: Raw metadata filter

    Returns:
        Converted Qdrant filter or None if invalid
    """
    if not metadata_filter or not isinstance(metadata_filter, dict):
        return None

    try:
        filter_conditions = []

        for key, value in metadata_filter.items():
            # Validate key
            if not isinstance(key, str):
                logger.warning(f"Invalid filter key type: {type(key)}")
                continue

            # Handle different value types
            if isinstance(value, list):
                # For lists, use 'any' condition
                filter_conditions.append({"key": key, "any": {"values": value}})
            elif isinstance(value, dict) and "range" in value:
                # For range queries, validate range parameters
                range_params = {}
                valid_range_keys = {"gt", "gte", "lt", "lte"}

                for range_key, range_value in value["range"].items():
                    if range_key in valid_range_keys:
                        range_params[range_key] = range_value

                if range_params:
                    filter_conditions.append({"key": key, "range": range_params})
            else:
                # For exact matches, validate value type
                if value is not None:
                    filter_conditions.append({"key": key, "match": {"value": value}})

        if filter_conditions:
            return {"must": filter_conditions}

        return None

    except Exception as e:
        logger.error(f"Error converting metadata filter: {str(e)}")
        return None
