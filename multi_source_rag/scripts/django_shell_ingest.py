"""
Django shell script to ingest Slack data using the improved LocalIngestionService.

To run this script:
1. Open a terminal in your project directory
2. Run the Django shell:
   python manage.py shell
3. In the shell, run:
   exec(open('django_shell_ingest.py').read())
"""

import json
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import Django models
from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk, EmbeddingMetadata
from apps.documents.services.local_ingestion_service import LocalIngestionService

# Clean existing data
print("Cleaning existing data...")
EmbeddingMetadata.objects.all().delete()
DocumentChunk.objects.all().delete()
RawDocument.objects.all().delete()
DocumentSource.objects.filter(source_type="local_slack").delete()
print("Data cleaned.")

# Get or create default tenant
tenant, _ = Tenant.objects.get_or_create(
    slug="default",
    defaults={"name": "Default Tenant"}
)

# Create document source with two-year time period configuration
config = {
    "data_dir": "../data/",  # Path to the parent directory containing channel_C065QSSNH8A
    "time_period": "custom",
    "custom_days": 730,  # Two years
    "enable_semantic_cross_refs": True,
    "quality_threshold": 0.3,
}

source = DocumentSource.objects.create(
    tenant=tenant,
    name="Local Slack Data",
    source_type="local_slack",
    config=config,
    is_active=True
)
print(f"Created document source: {source.name}")

# Use the actual ingestion service
ingestion_service = LocalIngestionService(tenant=tenant)

# Get source interface
print("Getting source interface...")
source_interface = ingestion_service._get_source_interface(source)

# Fetch documents
print("Fetching documents...")
documents = source_interface.fetch_documents()
print(f"Fetched {len(documents)} documents")

# Debug document structure
if documents:
    first_doc = documents[0]
    print(f"First document type: {type(first_doc)}")
    if isinstance(first_doc, dict):
        print(f"First document keys: {first_doc.keys()}")
        for key, value in first_doc.items():
            print(f"Key: {key}, Value type: {type(value)}")
            if key == "metadata" and isinstance(value, dict):
                print(f"Metadata keys: {value.keys()}")
    else:
        print(f"First document content: {first_doc[:100]}...")

# Process the source
print("Starting ingestion process...")
processed, failed = ingestion_service.process_source(source)
print(f"Ingestion result: {processed} documents processed, {failed} documents failed")

# Print statistics
doc_count = RawDocument.objects.filter(source=source).count()
chunk_count = DocumentChunk.objects.filter(document__source=source).count()
print(f"Created {doc_count} documents and {chunk_count} chunks")

# Print document details
for doc in RawDocument.objects.filter(source=source):
    try:
        metadata = json.loads(doc.metadata) if isinstance(doc.metadata, str) else doc.metadata
        chunking_strategy = metadata.get('chunking_strategy', 'unknown')
        period_type = metadata.get('period_type', 'unknown')
        message_count = metadata.get('message_count', 0)
        thread_count = metadata.get('thread_count', 0)

        print(f"Document: {doc.title}")
        print(f"  - Chunking Strategy: {chunking_strategy}")
        print(f"  - Period Type: {period_type}")
        print(f"  - Message Count: {message_count}")
        print(f"  - Thread Count: {thread_count}")
        print(f"  - Chunks: {DocumentChunk.objects.filter(document=doc).count()}")
    except Exception as e:
        print(f"Error parsing metadata for {doc.title}: {e}")

# Print chunk statistics
chunks = DocumentChunk.objects.filter(document__source=source)
if chunks.exists():
    # Calculate average chunk length
    total_length = sum(len(chunk.text) for chunk in chunks)
    avg_length = total_length / chunks.count()

    # Calculate min and max chunk lengths
    min_length = min(len(chunk.text) for chunk in chunks)
    max_length = max(len(chunk.text) for chunk in chunks)

    print(f"\nChunk Statistics:")
    print(f"  - Total Chunks: {chunks.count()}")
    print(f"  - Average Chunk Length: {avg_length:.2f} characters")
    print(f"  - Min Chunk Length: {min_length} characters")
    print(f"  - Max Chunk Length: {max_length} characters")

    # Count chunks with thread markers
    thread_chunks = sum(1 for chunk in chunks if "Thread ID:" in chunk.text)
    print(f"  - Chunks with Thread ID: {thread_chunks} ({thread_chunks/chunks.count()*100:.2f}%)")

    # Print first chunk preview
    if chunks.exists():
        first_chunk = chunks.first()
        print(f"\nFirst Chunk Preview: {first_chunk.text[:100]}...")

print("\nIngestion completed successfully!")
