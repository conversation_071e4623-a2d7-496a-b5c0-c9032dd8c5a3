#!/usr/bin/env python
"""
Script to clean the database by removing all documents, chunks, and related data.
"""

import os
import django
import logging
from datetime import datetime

# Set up Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
django.setup()

from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk, EmbeddingMetadata
from apps.documents.models import ChunkRelationship, DocumentProcessingJob

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clean_database():
    """Clean the database by removing all documents, chunks, and related data."""
    logger.info("Starting database cleaning process...")

    # Delete all embedding metadata
    embedding_count = EmbeddingMetadata.objects.count()
    EmbeddingMetadata.objects.all().delete()
    logger.info(f"Deleted {embedding_count} embedding metadata records")

    # Delete all chunk relationships
    relationship_count = ChunkRelationship.objects.count()
    ChunkRelationship.objects.all().delete()
    logger.info(f"Deleted {relationship_count} chunk relationships")

    # Delete all document chunks
    chunk_count = DocumentChunk.objects.count()
    DocumentChunk.objects.all().delete()
    logger.info(f"Deleted {chunk_count} document chunks")

    # Delete all raw documents
    doc_count = RawDocument.objects.count()
    RawDocument.objects.all().delete()
    logger.info(f"Deleted {doc_count} raw documents")

    # Delete all document processing jobs
    job_count = DocumentProcessingJob.objects.count()
    DocumentProcessingJob.objects.all().delete()
    logger.info(f"Deleted {job_count} document processing jobs")

    # Delete all document sources
    source_count = DocumentSource.objects.count()
    DocumentSource.objects.all().delete()
    logger.info(f"Deleted {source_count} document sources")

    # Note: UserSession, Conversation, and Message models are not available in this codebase
    # We'll skip cleaning these tables

    # Check if vector database needs cleaning
    try:
        from apps.core.utils.vectorstore import get_qdrant_client

        # Get Qdrant client
        client = get_qdrant_client()

        # Get all tenants
        tenants = Tenant.objects.all()

        # Delete collections for each tenant
        for tenant in tenants:
            collection_name = f"{tenant.slug}_chunks"
            try:
                if client.collection_exists(collection_name):
                    client.delete_collection(collection_name)
                    logger.info(f"Deleted vector collection: {collection_name}")
            except Exception as e:
                logger.error(f"Error deleting vector collection {collection_name}: {str(e)}")
    except Exception as e:
        logger.error(f"Error cleaning vector database: {str(e)}")

    logger.info("Database cleaning completed successfully")

if __name__ == "__main__":
    clean_database()
