{"thread_ts": "1733757704.559669", "channel_id": "C065QSSNH8A", "reply_count": 4, "replies": [{"ts": "1733758144.675249", "text": "We need check with them if they are able to send files to SFTP.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733757704.559669", "blocks": [{"type": "rich_text", "block_id": "x/ihg", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We need check with them if they are able to send files to SFTP."}]}]}]}, {"ts": "1733758182.465199", "text": "What do you mean? That is what they've been creating. Do we need to send them a location?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733757704.559669", "blocks": [{"type": "rich_text", "block_id": "ZdQO2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "What do you mean? That is what they've been creating. Do we need to send them a location?"}]}]}]}, {"ts": "1733758277.681639", "text": "We had shared earlier. We need to check if they are able place the file there and ask them to send the file to SFTP. I will pick it up from there for testing.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733757704.559669", "blocks": [{"type": "rich_text", "block_id": "6c99x", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We had shared earlier. We need to check if they are able place the file there and ask them to send the file to SFTP. I will pick it up from there for testing."}]}]}]}, {"ts": "1733758421.216779", "text": "Ok great. Will do", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733757704.559669", "blocks": [{"type": "rich_text", "block_id": "UoRY/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok great. Will do"}]}]}]}], "created_at": "2025-05-22T22:00:49.317498"}