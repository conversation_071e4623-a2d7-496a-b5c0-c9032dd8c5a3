{"thread_ts": "1733724451.259499", "channel_id": "C065QSSNH8A", "reply_count": 9, "replies": [{"ts": "1733758077.374639", "text": "Are you also going to show the toggle for live and submitted people insights?", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733724451.259499", "blocks": [{"type": "rich_text", "block_id": "RSHaT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Are you also going to show the toggle for live and submitted people insights?"}]}]}]}, {"ts": "1733758099.320909", "text": "That will be tomorrow. <@U07M6QKHUC9>", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733724451.259499", "blocks": [{"type": "rich_text", "block_id": "Dqhiv", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "That will be tomorrow. "}, {"type": "user", "user_id": "U07M6QKHUC9"}]}]}]}, {"ts": "1733758230.998859", "text": "This is just a wishlist, but as I check on people's cycles, it would be super useful to show that completion graph on the task page inside the merit view.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733724451.259499", "blocks": [{"type": "rich_text", "block_id": "D1sG6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is just a wishlist, but as I check on people's cycles, it would be super useful to show that completion graph on the task page inside the merit view."}]}]}]}, {"ts": "1733758307.936789", "text": "<@U07EJ2LP44S> Please add this to the doc.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733724451.259499", "blocks": [{"type": "rich_text", "block_id": "fo4bW", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " Please add this to the doc."}]}]}]}, {"ts": "1733758403.201449", "text": "My ux doc? Is that the most useful place?", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733724451.259499", "blocks": [{"type": "rich_text", "block_id": "+6OyP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "My ux doc? Is that the most useful place?"}]}]}]}, {"ts": "1733758546.837539", "text": "even ticket is better. We definitely going to lose track of these if not documented.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733724451.259499", "blocks": [{"type": "rich_text", "block_id": "saIag", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "even ticket is better. We definitely going to lose track of these if not documented."}]}]}]}, {"ts": "1733759290.000679", "text": "We have multiple UX bugs document in Jira and I'm worried they are getting lost there too. There's one for the paybands sorting and one for the clear filters usability both inside SDF.", "user": "U07EJ2LP44S", "type": "message", "thread_ts": "1733724451.259499", "blocks": [{"type": "rich_text", "block_id": "2fs/7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We have multiple UX bugs document in Jira and I'm worried they are getting lost there too. There's one for the paybands sorting and one for the clear filters usability both inside SDF."}]}]}]}, {"ts": "1733759494.059909", "text": "as long as the tickets are created and labeled as priorotized. We won't lose them. I will keep track and triage those tickets as we go.", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733724451.259499", "reactions": [{"name": "+1", "users": ["U07EJ2LP44S"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "LIMdc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "as long as the tickets are created and labeled as priorotized. We won't lose them. I will keep track and triage those tickets as we go."}]}]}]}, {"ts": "1733759505.847169", "text": "based on priority and capacity", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733724451.259499", "blocks": [{"type": "rich_text", "block_id": "DraPk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "based on priority and capacity"}]}]}]}], "created_at": "2025-05-22T22:00:49.317621"}