{"thread_ts": "1733498510.956169", "channel_id": "C065QSSNH8A", "reply_count": 4, "replies": [{"ts": "1733509885.728389", "text": "majority of her feedback should be addressed by Phase 1 UX improvements and tabs  functionality that we have planned for Dec. <@U0690EB5JE5> are you aligned on that?\n\nWe discussed putting modifier in the column header last week as that's how it is in their spreadsheet and it makes total sense.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733498510.956169", "blocks": [{"type": "rich_text", "block_id": "nR3Uq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "majority of her feedback should be addressed by Phase 1 UX improvements and tabs  functionality that we have planned for Dec. "}, {"type": "user", "user_id": "U0690EB5JE5"}, {"type": "text", "text": " are you aligned on that?\n\nWe discussed putting modifier in the column header last week as that's how it is in their spreadsheet and it makes total sense."}]}]}]}, {"ts": "1733534732.959809", "text": "Header thing can be handled with column configurator. ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733498510.956169", "blocks": [{"type": "rich_text", "block_id": "uM0xt", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Header thing can be handled with column configurator. "}]}]}]}, {"ts": "1733534753.167609", "text": "<@U07M6QKHUC9> we can align when we meet on the scope and requirements ", "user": "U0690EB5JE5", "type": "message", "thread_ts": "1733498510.956169", "reactions": [{"name": "+1", "users": ["U07M6QKHUC9"], "count": 1}], "blocks": [{"type": "rich_text", "block_id": "KlUMM", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07M6QKHUC9"}, {"type": "text", "text": " we can align when we meet on the scope and requirements "}]}]}]}, {"ts": "1733791313.113279", "text": "<@U07EJ2LP44S> can you list which of the items in Diversified's are not covered by <PERSON>'s Phase 1- Epic 1 &amp; 2 PRDs so <PERSON><PERSON><PERSON> has clarity on what additional work needs to be done. We should create tickets for those items.", "user": "U07M6QKHUC9", "type": "message", "thread_ts": "1733498510.956169", "subtype": "thread_broadcast", "blocks": [{"type": "rich_text", "block_id": "ck+oT", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U07EJ2LP44S"}, {"type": "text", "text": " can you list which of the items in Diversified's are not covered by <PERSON>'s Phase 1- Epic 1 & 2 PRDs so <PERSON><PERSON><PERSON> has clarity on what additional work needs to be done. We should create tickets for those items."}]}]}]}], "created_at": "2025-05-22T22:00:49.318512"}